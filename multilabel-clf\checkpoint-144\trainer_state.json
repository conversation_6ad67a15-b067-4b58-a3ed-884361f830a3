{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 3.0, "eval_steps": 500, "global_step": 144, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 1.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.3055359125137329, "eval_per_label_accuracy": 0.9399509803921569, "eval_runtime": 0.2869, "eval_samples_per_second": 334.633, "eval_steps_per_second": 41.829, "step": 48}, {"epoch": 2.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.23606781661510468, "eval_per_label_accuracy": 0.9399509803921569, "eval_runtime": 0.2833, "eval_samples_per_second": 338.883, "eval_steps_per_second": 42.36, "step": 96}], "logging_steps": 500, "max_steps": 144, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 8, "trial_name": null, "trial_params": null}