import torch
import pandas as pd

from datasets import Dataset
from transformers import AutoTokenizer, AutoModelForSequenceClassification

from sklearn.preprocessing import MultiLabelBinarizer
from transformers import TrainingArguments, Trainer, DataCollatorWithPadding
from sklearn.metrics import accuracy_score, classification_report
import numpy as np
from transformers import AutoModelForSequenceClassification
import torch.nn as nn
from transformers.modeling_outputs import SequenceClassifierOutput

USE_CUDA = True
DEVICE = "cuda" if USE_CUDA and torch.cuda.is_available() else "cpu"


# Can add more labels here
labels = [
    "rfq_scope",
    "general_category",
    "unit_of_measure",
    "material",
    "abbreviated_material",
    "astm",
    "grade",
    "rating",
    "schedule",
    "coating",
    "forging",
    "ends",
    "pipe_category",
    "valve_type",
    "fitting_category",
]

class MultiLabelClassifier(nn.Module):
    def __init__(self, base_model_name, num_labels):
        super().__init__()
        self.base = AutoModelForSequenceClassification.from_pretrained(
            base_model_name,
            num_labels=num_labels,
            problem_type="multi_label_classification"
        )

    def forward(self, input_ids=None, attention_mask=None, token_type_ids=None,
                position_ids=None, head_mask=None, inputs_embeds=None, labels=None,
                output_attentions=None, output_hidden_states=None, return_dict=None, **kwargs):
        # Filter out any unexpected arguments
        valid_args = {
            'input_ids': input_ids,
            'attention_mask': attention_mask,
            'token_type_ids': token_type_ids,
            'position_ids': position_ids,
            'head_mask': head_mask,
            'inputs_embeds': inputs_embeds,
            'labels': labels,
            'output_attentions': output_attentions,
            'output_hidden_states': output_hidden_states,
            'return_dict': return_dict
        }
        # Remove None values
        valid_args = {k: v for k, v in valid_args.items() if v is not None}

        return self.base(**valid_args)


def prepare_training_set(file: str, test_size: float = 0.2, model_name = "bert-base-uncased"):
    df = pd.read_excel(file)

    # Combine all label columns into a multi-label target
    # Fill NaN values with empty string before converting to string
    df_labels = df[labels].fillna('')
    df["labels"] = df_labels.astype(str).values.tolist()

    # Binarize labels
    global mlb  # Make mlb accessible globally
    mlb = MultiLabelBinarizer()
    label_vectors = mlb.fit_transform(df["labels"])
    df["label_vector"] = label_vectors.tolist()

    print(f"Number of unique labels: {len(mlb.classes_)}")
    print(f"Label classes: {mlb.classes_[:10]}...")  # Show first 10 classes
    print(f"Label vector shape: {label_vectors.shape}")
    print(f"Sample label vector: {label_vectors[0]}")

    # Check label distribution
    label_sums = label_vectors.sum(axis=0)
    print(f"Label frequency (top 10): {sorted(zip(mlb.classes_, label_sums), key=lambda x: x[1], reverse=True)[:10]}")
    print(f"Average labels per sample: {label_vectors.sum(axis=1).mean():.2f}")

    # Hugging Face dataset
    dataset = Dataset.from_pandas(df[['material_description', 'label_vector']])
    dataset = dataset.train_test_split(test_size=test_size)

    # Load a T5 tokenizer and model from Hugging Face
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = MultiLabelClassifier("bert-base-uncased", num_labels=len(mlb.classes_))
    model.to(DEVICE)

    def preprocess(examples):
        # Ensure material_description is a list of strings
        descriptions = examples["material_description"]

        # Handle both single examples and batches
        if isinstance(descriptions, list):
            # Make sure each item is a string
            descriptions = [str(item) if item is not None else "" for item in descriptions]
        else:
            # If it's not a list, convert it to a string and wrap in a list
            descriptions = [str(descriptions) if descriptions is not None else ""]

        # Tokenize with consistent parameters - don't use padding=True in batched mode
        tokenized = tokenizer(
            descriptions,
            truncation=True,
            padding=False,  # Let the trainer handle padding
            max_length=128,  # Increased max length for better context
            return_tensors=None  # Don't return tensors, let trainer handle it
        )

        # Handle labels properly for batched processing
        label_vectors = examples["label_vector"]
        if isinstance(label_vectors, list) and len(label_vectors) > 0:
            # Check if we have a batch of label vectors
            if isinstance(label_vectors[0], list):
                # Convert to float for multi-label classification
                tokenized["labels"] = [[float(x) for x in label_vec] for label_vec in label_vectors]
            else:
                # Single label vector, wrap in list and convert to float
                tokenized["labels"] = [[float(x) for x in label_vectors]]
        else:
            # Fallback for edge cases
            if isinstance(label_vectors, list):
                tokenized["labels"] = [[float(x) for x in label_vectors]]
            else:
                tokenized["labels"] = [[float(label_vectors)]]

        return tokenized

    tokenized = dataset.map(preprocess, batched=True)

    # Remove the original columns that are not needed for training
    tokenized = tokenized.remove_columns(['material_description', 'label_vector'])

    return dataset, model, tokenizer, tokenized


def train_model(model, tokenizer, tokenized):
    """
    Train a sequence classification model on the dataset

    Args:
        model: A pre-trained model instance
        tokenizer: A tokenizer instance
        tokenized: A tokenized dataset instance

    Returns:
        A trained model instance
    """
    def compute_metrics(eval_pred):
        logits, labels = eval_pred
        # For multi-label classification, use sigmoid and threshold
        predictions = (torch.sigmoid(torch.tensor(logits)) > 0.5).int().numpy()

        # Calculate accuracy for multi-label (exact match)
        exact_match = np.all(predictions == labels, axis=1).mean()

        # Calculate per-label accuracy
        per_label_acc = np.mean(predictions == labels)

        return {
            "exact_match_accuracy": exact_match,
            "per_label_accuracy": per_label_acc
        }

    args = TrainingArguments(
        output_dir="./multilabel-clf",
        per_device_train_batch_size=8,
        per_device_eval_batch_size=8,
        num_train_epochs=5,  # Increased epochs
        eval_strategy="epoch",
        save_strategy="epoch",
        logging_steps=10,
        learning_rate=2e-5,  # Explicit learning rate
        weight_decay=0.01,   # Add weight decay for regularization
        warmup_steps=100,    # Add warmup steps
        remove_unused_columns=False,
        load_best_model_at_end=True,
        metric_for_best_model="per_label_accuracy",
        greater_is_better=True,
    )

    # Create data collator for proper padding
    data_collator = DataCollatorWithPadding(tokenizer=tokenizer)

    trainer = Trainer(
        model=model,
        args=args,
        train_dataset=tokenized["train"],
        eval_dataset=tokenized["test"],
        tokenizer=tokenizer,
        data_collator=data_collator,
        compute_metrics=compute_metrics
    )

    trainer.train()

def run_inference(tokenizer, model, text: str):
    """
    Make Predictions

    """
    inputs = tokenizer(text, return_tensors="pt")
    # Move to the same device as the model
    inputs = {k: v.to(DEVICE) for k, v in inputs.items()}

    # Set model to evaluation mode. Deterministic predictions across runs
    model.eval()

    # Inference
    with torch.no_grad():
        logits = model(**inputs).logits
        preds = (logits > 0).int().cpu().numpy()[0]
        # Convert to numpy array for inverse_transform
        preds_array = np.array([preds])
        predicted_labels = mlb.inverse_transform(preds_array)[0]
        print(f"Text: {text}")
        print(f"Predicted Labels: {predicted_labels}")
        print("---")

def set_seed(seed=42):
    """
    Set a seed (e.g. 42) to ensure reproducibility
    Without it, the same script can give slightly different model weights and performance each run
    """
    import random
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)

set_seed(42)


def run(filename: str, test_file: str=None):
    """
    Example training process and inference test
    """
    dataset, model, tokenizer, tokenized = prepare_training_set(filename)
    train_model(model, tokenizer, tokenized)

    if not test_file:
        return

    test_df = pd.read_excel(test_file)
    descriptions = test_df["material_description"].unique().tolist()
    for text in descriptions:
        run_inference(tokenizer, model, text)

if __name__ == "__main__":
    training_file = r"src\training\data\rfq_template-example.xlsx"
    test_file = r"src\training\data\rfq_template-example.xlsx"
    run(training_file, test_file)