{"best_global_step": 48, "best_metric": 0.9067028985507246, "best_model_checkpoint": "./multilabel-clf\\checkpoint-48", "epoch": 1.0, "eval_steps": 500, "global_step": 48, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.20833333333333334, "grad_norm": 1.564944863319397, "learning_rate": 1.8000000000000001e-06, "loss": 0.7057, "step": 10}, {"epoch": 0.4166666666666667, "grad_norm": 1.3470088243484497, "learning_rate": 3.8000000000000005e-06, "loss": 0.683, "step": 20}, {"epoch": 0.625, "grad_norm": 3.190873384475708, "learning_rate": 5.8e-06, "loss": 0.6457, "step": 30}, {"epoch": 0.8333333333333334, "grad_norm": 1.584578514099121, "learning_rate": 7.800000000000002e-06, "loss": 0.6033, "step": 40}, {"epoch": 1.0, "eval_exact_match_accuracy": 0.07291666666666667, "eval_loss": 0.5179724097251892, "eval_per_label_accuracy": 0.9067028985507246, "eval_runtime": 0.2771, "eval_samples_per_second": 346.444, "eval_steps_per_second": 43.306, "step": 48}], "logging_steps": 10, "max_steps": 240, "num_input_tokens_seen": 0, "num_train_epochs": 5, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 8, "trial_name": null, "trial_params": null}