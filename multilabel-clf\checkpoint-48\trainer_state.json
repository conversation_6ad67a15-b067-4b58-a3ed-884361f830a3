{"best_global_step": 48, "best_metric": 0.7266260162601627, "best_model_checkpoint": "./multilabel-clf\\checkpoint-48", "epoch": 1.0, "eval_steps": 500, "global_step": 48, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.20833333333333334, "grad_norm": 0.7535103559494019, "learning_rate": 1.8000000000000001e-06, "loss": 0.723, "step": 10}, {"epoch": 0.4166666666666667, "grad_norm": 0.732583224773407, "learning_rate": 3.8000000000000005e-06, "loss": 0.7109, "step": 20}, {"epoch": 0.625, "grad_norm": 0.8079116940498352, "learning_rate": 5.8e-06, "loss": 0.6893, "step": 30}, {"epoch": 0.8333333333333334, "grad_norm": 0.6915093660354614, "learning_rate": 7.800000000000002e-06, "loss": 0.6536, "step": 40}, {"epoch": 1.0, "eval_exact_match_accuracy": 0.0, "eval_loss": 0.6045021414756775, "eval_per_label_accuracy": 0.7266260162601627, "eval_runtime": 0.2761, "eval_samples_per_second": 347.72, "eval_steps_per_second": 43.465, "step": 48}], "logging_steps": 10, "max_steps": 240, "num_input_tokens_seen": 0, "num_train_epochs": 5, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 8, "trial_name": null, "trial_params": null}