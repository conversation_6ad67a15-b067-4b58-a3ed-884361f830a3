{"best_global_step": 192, "best_metric": 0.9433876811594203, "best_model_checkpoint": "./multilabel-clf\\checkpoint-192", "epoch": 5.0, "eval_steps": 500, "global_step": 240, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.20833333333333334, "grad_norm": 1.564944863319397, "learning_rate": 1.8000000000000001e-06, "loss": 0.7057, "step": 10}, {"epoch": 0.4166666666666667, "grad_norm": 1.3470088243484497, "learning_rate": 3.8000000000000005e-06, "loss": 0.683, "step": 20}, {"epoch": 0.625, "grad_norm": 3.190873384475708, "learning_rate": 5.8e-06, "loss": 0.6457, "step": 30}, {"epoch": 0.8333333333333334, "grad_norm": 1.584578514099121, "learning_rate": 7.800000000000002e-06, "loss": 0.6033, "step": 40}, {"epoch": 1.0, "eval_exact_match_accuracy": 0.07291666666666667, "eval_loss": 0.5179724097251892, "eval_per_label_accuracy": 0.9067028985507246, "eval_runtime": 0.2771, "eval_samples_per_second": 346.444, "eval_steps_per_second": 43.306, "step": 48}, {"epoch": 1.0416666666666667, "grad_norm": 1.2381452322006226, "learning_rate": 9.800000000000001e-06, "loss": 0.5484, "step": 50}, {"epoch": 1.25, "grad_norm": 1.3342211246490479, "learning_rate": 1.18e-05, "loss": 0.492, "step": 60}, {"epoch": 1.4583333333333333, "grad_norm": 1.2425239086151123, "learning_rate": 1.38e-05, "loss": 0.4432, "step": 70}, {"epoch": 1.6666666666666665, "grad_norm": 1.3584648370742798, "learning_rate": 1.58e-05, "loss": 0.4001, "step": 80}, {"epoch": 1.875, "grad_norm": 0.8094673752784729, "learning_rate": 1.7800000000000002e-05, "loss": 0.3518, "step": 90}, {"epoch": 2.0, "eval_exact_match_accuracy": 0.052083333333333336, "eval_loss": 0.30853620171546936, "eval_per_label_accuracy": 0.927536231884058, "eval_runtime": 0.2765, "eval_samples_per_second": 347.142, "eval_steps_per_second": 43.393, "step": 96}, {"epoch": 2.0833333333333335, "grad_norm": 0.774901807308197, "learning_rate": 1.98e-05, "loss": 0.3172, "step": 100}, {"epoch": 2.2916666666666665, "grad_norm": 0.6260517239570618, "learning_rate": 1.8714285714285717e-05, "loss": 0.2925, "step": 110}, {"epoch": 2.5, "grad_norm": 0.6284401416778564, "learning_rate": 1.7285714285714287e-05, "loss": 0.2701, "step": 120}, {"epoch": 2.7083333333333335, "grad_norm": 0.5581534504890442, "learning_rate": 1.5857142857142857e-05, "loss": 0.26, "step": 130}, {"epoch": 2.9166666666666665, "grad_norm": 0.6461836099624634, "learning_rate": 1.4428571428571429e-05, "loss": 0.2536, "step": 140}, {"epoch": 3.0, "eval_exact_match_accuracy": 0.28125, "eval_loss": 0.2364368438720703, "eval_per_label_accuracy": 0.9379528985507246, "eval_runtime": 0.2725, "eval_samples_per_second": 352.255, "eval_steps_per_second": 44.032, "step": 144}, {"epoch": 3.125, "grad_norm": 0.4610616862773895, "learning_rate": 1.3000000000000001e-05, "loss": 0.2348, "step": 150}, {"epoch": 3.3333333333333335, "grad_norm": 0.656665027141571, "learning_rate": 1.1571428571428573e-05, "loss": 0.2333, "step": 160}, {"epoch": 3.5416666666666665, "grad_norm": 0.49339064955711365, "learning_rate": 1.0142857142857143e-05, "loss": 0.2349, "step": 170}, {"epoch": 3.75, "grad_norm": 0.46317628026008606, "learning_rate": 8.714285714285715e-06, "loss": 0.2129, "step": 180}, {"epoch": 3.9583333333333335, "grad_norm": 0.5191589593887329, "learning_rate": 7.285714285714286e-06, "loss": 0.2222, "step": 190}, {"epoch": 4.0, "eval_exact_match_accuracy": 0.28125, "eval_loss": 0.21334882080554962, "eval_per_label_accuracy": 0.9433876811594203, "eval_runtime": 0.2764, "eval_samples_per_second": 347.384, "eval_steps_per_second": 43.423, "step": 192}, {"epoch": 4.166666666666667, "grad_norm": 0.39443299174308777, "learning_rate": 5.857142857142858e-06, "loss": 0.226, "step": 200}, {"epoch": 4.375, "grad_norm": 0.6173935532569885, "learning_rate": 4.428571428571429e-06, "loss": 0.2052, "step": 210}, {"epoch": 4.583333333333333, "grad_norm": 0.39556604623794556, "learning_rate": 3e-06, "loss": 0.2067, "step": 220}, {"epoch": 4.791666666666667, "grad_norm": 0.48841482400894165, "learning_rate": 1.5714285714285714e-06, "loss": 0.1996, "step": 230}, {"epoch": 5.0, "grad_norm": 0.6777200102806091, "learning_rate": 1.4285714285714287e-07, "loss": 0.2118, "step": 240}, {"epoch": 5.0, "eval_exact_match_accuracy": 0.28125, "eval_loss": 0.2053271383047104, "eval_per_label_accuracy": 0.9433876811594203, "eval_runtime": 0.2608, "eval_samples_per_second": 368.093, "eval_steps_per_second": 46.012, "step": 240}], "logging_steps": 10, "max_steps": 240, "num_input_tokens_seen": 0, "num_train_epochs": 5, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 8, "trial_name": null, "trial_params": null}